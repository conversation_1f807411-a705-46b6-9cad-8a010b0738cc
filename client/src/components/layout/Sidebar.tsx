import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../ui/button';
import {
  LayoutDashboard,
  Users,
  Settings,
  Database,
  FileText,
  ChevronRight,
  Bell,
  ChevronDown,
  LogOut,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile?: boolean;
}

export function Sidebar({ isOpen, onClose, isMobile = false }: SidebarProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  // Get current page from URL path
  const currentPage = location.pathname.replace('/superadmin/', '').replace('/', '') || 'dashboard';

  // Check if any submenu item is active to auto-expand
  const pengaturanSistemPages = ['manajemen-pengguna', 'manajemen-konten', 'role-management'];
  const isPengaturanSistemActive = pengaturanSistemPages.includes(currentPage);
  const [pengaturanSistemOpen, setPengaturanSistemOpen] = useState(isPengaturanSistemActive);

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },
    { id: 'manajemen-pengunjung', label: 'Manajemen Pengunjung', icon: Users },
    {
      id: 'pengaturan-sistem',
      label: 'Pengaturan Sistem',
      icon: Settings,
      hasSubmenu: true,
      submenu: [
        { id: 'manajemen-pengguna', label: 'Manajemen Pengguna' },
        { id: 'manajemen-konten', label: 'Manajemen Konten' },
        { id: 'role-management', label: 'Role Management' }
      ]
    },
    { id: 'backup-restore', label: 'Backup & Restore', icon: Database },
    { id: 'logs', label: 'Logs', icon: FileText },
  ];

  const handleNavigation = (itemId: string) => {
    navigate(`/superadmin/${itemId}`);
    onClose();
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
      onClose();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <>
      {/* Mobile Sidebar Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden backdrop-blur-sm"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        ${isMobile ? 'fixed' : 'relative'} left-0 top-0 bottom-0 z-40 w-64 bg-white border-r border-gray-200
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        ${!isMobile ? 'lg:translate-x-0' : ''}
        ${isMobile ? 'h-full' : 'min-h-screen'}
      `}>
        <nav className="h-full flex flex-col">
          {/* Mobile Header Items */}
          {isMobile && (
            <div className="p-4 border-b border-gray-200 space-y-4">
              

              {/* User Menu for Mobile */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-blue-600 text-white text-xs">
                      {user ? getUserInitials(user.name) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{user?.name || 'User'}</span>
                    <span className="text-xs text-gray-500 capitalize">{user?.role?.toLowerCase() || 'user'}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" className="p-2">
                    <Bell className="h-4 w-4" />
                  </Button>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="p-2">
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      <DropdownMenuItem>
                        <User className="mr-2 h-4 w-4" />
                        Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                        <LogOut className="mr-2 h-4 w-4" />
                        Logout
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          )}

          {/* Desktop Logo - Only show on desktop */}
         
          {/* Navigation Menu */}
          <div className="flex-1 overflow-y-auto px-4 py-4 space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              const isSubmenuActive = item.submenu?.some(sub => currentPage === sub.id);
              const shouldShowSubmenu = item.hasSubmenu && (pengaturanSistemOpen || isSubmenuActive);

              return (
                <div key={item.id}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    className={`w-full justify-start text-sm font-medium transition-colors ${
                      isActive || isSubmenuActive
                        ? "bg-orange-500 hover:bg-orange-600 text-white shadow-sm"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    }`}
                    onClick={() => {
                      if (item.hasSubmenu) {
                        setPengaturanSistemOpen(!pengaturanSistemOpen);
                      } else {
                        handleNavigation(item.id);
                      }
                    }}
                  >
                    <Icon className="mr-3 h-4 w-4 flex-shrink-0" />
                    <span className="truncate flex-1 text-left">{item.label}</span>
                    {item.hasSubmenu && (
                      <ChevronRight className={`h-4 w-4 flex-shrink-0 transition-transform ${
                        shouldShowSubmenu ? 'rotate-90' : ''
                      }`} />
                    )}
                  </Button>

                  {/* Submenu */}
                  {shouldShowSubmenu && item.submenu && (
                    <div className="ml-4 mt-1 space-y-1">
                      {item.submenu.map((subItem) => {
                        const isSubActive = currentPage === subItem.id;
                        return (
                          <Button
                            key={subItem.id}
                            variant="ghost"
                            className={`w-full justify-start text-sm transition-colors ${
                              isSubActive
                                ? "bg-orange-100 text-orange-700 hover:bg-orange-200"
                                : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                            }`}
                            onClick={() => handleNavigation(subItem.id)}
                          >
                            <span className="truncate pl-6">{subItem.label}</span>
                          </Button>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
            {/* Extra padding at bottom to prevent cut-off */}
            <div className="h-8"></div>
          </div>
        </nav>
      </aside>
    </>
  );
}
