import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import {
  Dashboard,
  ManajemenPengunjung,
  ManajemenPengguna,
  ManajemenKonten,
  RoleManagement,
  BackupRestore,
  Logs
} from './pages/superadmin';
import { SuperAdminLayout } from './pages/superadmin/SuperAdminLayout';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginPage } from './pages/auth/LoginPage';
import { SignupPage } from './pages/auth/SignupPage';
import { ForgotPasswordPage } from './pages/auth/ForgotPasswordPage';
import { UserDashboard } from './pages/UserDashboard';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />

          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/login" replace />} />

          {/* User dashboard - accessible by both USER and SUPERADMIN */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <UserDashboard />
            </ProtectedRoute>
          } />

          {/* Superadmin routes - only accessible by SUPERADMIN */}
          <Route path="/superadmin/dashboard" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <Dashboard />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/manajemen-pengunjung" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <ManajemenPengunjung />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/pengaturan-sistem" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <div className="text-center py-12">
                  <h2 className="text-xl font-semibold text-gray-900">Pengaturan Sistem</h2>
                  <p className="text-gray-600 mt-2">Halaman ini sedang dalam pengembangan</p>
                </div>
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/manajemen-pengguna" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <ManajemenPengguna />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/manajemen-konten" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <ManajemenKonten />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/role-management" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <RoleManagement />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/backup-restore" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <BackupRestore />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
          <Route path="/superadmin/logs" element={
            <ProtectedRoute requiredRole="SUPERADMIN">
              <SuperAdminLayout>
                <Logs />
              </SuperAdminLayout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App