import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function promoteUserToAdmin() {
  try {
    console.log("Promoting user to SUPERADMIN...");
    
    // Change this email to the user you want to promote
    const userEmail = "<EMAIL>"; // Change this to target user email
    
    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });

    if (!user) {
      console.error(`User with email ${userEmail} not found`);
      return;
    }

    console.log("Found user:", user.name, `(${user.email})`);
    console.log("Current role:", user.role);

    // Update user role to SUPERADMIN
    const updatedUser = await prisma.user.update({
      where: { email: userEmail },
      data: { role: "SUPERADMIN" }
    });

    console.log("✅ User promoted successfully!");
    console.log("Email:", updatedUser.email);
    console.log("Name:", updatedUser.name);
    console.log("New Role:", updatedUser.role);

  } catch (error) {
    console.error("Error promoting user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

promoteUserToAdmin();
