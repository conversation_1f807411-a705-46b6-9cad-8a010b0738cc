import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function listAllUsers() {
  try {
    console.log("📋 All Users in Database:");
    console.log("=" .repeat(50));
    
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' }
    });

    if (users.length === 0) {
      console.log("No users found");
      return;
    }

    users.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Created: ${user.createdAt.toLocaleDateString()}`);
      console.log(`   Banned: ${user.banned ? 'Yes' : 'No'}`);
      console.log("   " + "-".repeat(30));
    });

    console.log(`\nTotal users: ${users.length}`);
    console.log(`Superadmins: ${users.filter(u => u.role === 'SUPERADMIN').length}`);
    console.log(`Regular users: ${users.filter(u => u.role === 'USER').length}`);

  } catch (error) {
    console.error("Error listing users:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function changeUserRole(email: string, newRole: 'USER' | 'SUPERADMIN') {
  try {
    console.log(`Changing role for ${email} to ${newRole}...`);
    
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { role: newRole }
    });

    console.log("✅ Role updated successfully!");
    console.log(`${updatedUser.name} (${updatedUser.email}) is now ${updatedUser.role}`);

  } catch (error) {
    console.error("Error changing user role:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function banUser(email: string, reason: string = "No reason provided") {
  try {
    console.log(`Banning user ${email}...`);
    
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { 
        banned: true,
        banReason: reason
      }
    });

    console.log("✅ User banned successfully!");
    console.log(`${updatedUser.name} (${updatedUser.email}) has been banned`);
    console.log(`Reason: ${reason}`);

  } catch (error) {
    console.error("Error banning user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

async function unbanUser(email: string) {
  try {
    console.log(`Unbanning user ${email}...`);
    
    const updatedUser = await prisma.user.update({
      where: { email },
      data: { 
        banned: false,
        banReason: null,
        banExpires: null
      }
    });

    console.log("✅ User unbanned successfully!");
    console.log(`${updatedUser.name} (${updatedUser.email}) has been unbanned`);

  } catch (error) {
    console.error("Error unbanning user:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Main function to run based on command line arguments
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'list':
      await listAllUsers();
      break;
    
    case 'promote':
      const emailToPromote = args[1];
      if (!emailToPromote) {
        console.error("Please provide email: bun run src/scripts/manage-users.<NAME_EMAIL>");
        return;
      }
      await changeUserRole(emailToPromote, 'SUPERADMIN');
      break;
    
    case 'demote':
      const emailToDemote = args[1];
      if (!emailToDemote) {
        console.error("Please provide email: bun run src/scripts/manage-users.<NAME_EMAIL>");
        return;
      }
      await changeUserRole(emailToDemote, 'USER');
      break;
    
    case 'ban':
      const emailToBan = args[1];
      const banReason = args[2] || "No reason provided";
      if (!emailToBan) {
        console.error("Please provide email: bun run src/scripts/manage-users.<NAME_EMAIL> 'reason'");
        return;
      }
      await banUser(emailToBan, banReason);
      break;
    
    case 'unban':
      const emailToUnban = args[1];
      if (!emailToUnban) {
        console.error("Please provide email: bun run src/scripts/manage-users.<NAME_EMAIL>");
        return;
      }
      await unbanUser(emailToUnban);
      break;
    
    default:
      console.log("Available commands:");
      console.log("  list                           - List all users");
      console.log("  promote <email>                - Promote user to SUPERADMIN");
      console.log("  demote <email>                 - Demote SUPERADMIN to USER");
      console.log("  ban <email> [reason]           - Ban a user");
      console.log("  unban <email>                  - Unban a user");
      console.log("");
      console.log("Examples:");
      console.log("  bun run src/scripts/manage-users.ts list");
      console.log("  bun run src/scripts/manage-users.<NAME_EMAIL>");
      console.log("  bun run src/scripts/manage-users.<NAME_EMAIL> 'Spam'");
      break;
  }
}

main();
