import { PrismaClient } from "@prisma/client";
import { auth } from "../lib/auth";

const prisma = new PrismaClient();

async function createSuperAdmin() {
  try {
    console.log("Creating new superadmin...");

    // Prompt for user details (you can modify these)
    const email = "<EMAIL>"; // Change this
    const password = "admin1234"; // Change this
    const name = "Super Admin 2"; // Change this

    // Create new user using Better Auth
    const result = await auth.api.signUpEmail({
      body: {
        email,
        password,
        name,
      }
    });

    // Update user role to SUPERADMIN
    if (result.user?.id) {
      await prisma.user.update({
        where: { id: result.user.id },
        data: { role: "SUPERADMIN" }
      });

      console.log("✅ New superadmin created successfully!");
      console.log("Email:", email);
      console.log("Password:", password);
      console.log("Name:", name);
      console.log("Role: SUPERADMIN");
    } else {
      console.error("Failed to create user");
    }

  } catch (error) {
    console.error("Error creating superadmin:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createSuperAdmin();
