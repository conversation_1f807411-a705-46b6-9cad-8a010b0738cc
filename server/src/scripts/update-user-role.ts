import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function updateUserRole() {
  try {
    console.log("Checking users...");
    
    // List all users
    const users = await prisma.user.findMany();
    console.log("Current users:", users);

    // Update first user to SUPERADMIN if exists
    if (users.length > 0) {
      const firstUser = users[0];
      
      if (firstUser.role !== "SUPERADMIN") {
        await prisma.user.update({
          where: { id: firstUser.id },
          data: { role: "SUPERADMIN" }
        });
        
        console.log(`✅ Updated user ${firstUser.email} to SUPERADMIN role`);
      } else {
        console.log(`✅ User ${firstUser.email} already has SUPERADMIN role`);
      }
      
      console.log("Login credentials:");
      console.log("Email:", firstUser.email);
      console.log("Role: SUPERADMIN");
    } else {
      console.log("No users found in database");
    }

  } catch (error) {
    console.error("Error updating user role:", error);
  } finally {
    await prisma.$disconnect();
  }
}

updateUserRole();
