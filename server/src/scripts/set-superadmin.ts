import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function setSuperAdmin() {
  try {
    console.log("Setting superadmin role...");
    
    // Update specific user to SUPERADMIN
    const updatedUser = await prisma.user.update({
      where: { email: "<EMAIL>" },
      data: { role: "SUPERADMIN" }
    });
    
    console.log("✅ Updated user to SUPERADMIN:");
    console.log("Email: <EMAIL>");
    console.log("Password: admin1234");
    console.log("Role: SUPERADMIN");
    console.log("Name:", updatedUser.name);

  } catch (error) {
    console.error("Error setting superadmin role:", error);
  } finally {
    await prisma.$disconnect();
  }
}

setSuperAdmin();
