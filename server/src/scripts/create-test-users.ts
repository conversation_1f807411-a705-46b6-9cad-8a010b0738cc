import { PrismaClient } from "@prisma/client";
import { auth } from "../lib/auth";

const prisma = new PrismaClient();

async function createTestUsers() {
  try {
    console.log("Creating test users...");
    
    // Create superadmin
    try {
      const superadmin = await auth.api.signUpEmail({
        body: {
          email: "<EMAIL>",
          password: "admin1234",
          name: "Super Admin",
        }
      });

      if (superadmin.data?.user?.id) {
        await prisma.user.update({
          where: { id: superadmin.data.user.id },
          data: { role: "SUPERADMIN" }
        });

        console.log("✅ Superadmin created:");
        console.log("Email: <EMAIL>");
        console.log("Password: password123");
        console.log("Role: SUPERADMIN");
      }
    } catch (error) {
      console.log("Superadmin might already exist");
    }

    // Create regular user
    try {
      const user = await auth.api.signUpEmail({
        body: {
          email: "<EMAIL>",
          password: "password123",
          name: "Regular User",
        }
      });

      if (user.data?.user?.id) {
        console.log("✅ Regular user created:");
        console.log("Email: <EMAIL>");
        console.log("Password: password123");
        console.log("Role: USER");
      }
    } catch (error) {
      console.log("Regular user might already exist");
    }

  } catch (error) {
    console.error("Error creating test users:", error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();
