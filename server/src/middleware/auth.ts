import type { Context, Next } from "hono";
import { auth } from "../lib/auth";

export async function authMiddleware(c: Context, next: Next) {
  try {
    const session = await auth.api.getSession({
      headers: new Headers(c.req.header()),
    });

    if (!session) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Add user and session to context
    c.set("user", session.user);
    c.set("session", session.session);
    
    await next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return c.json({ error: "Authentication failed" }, 401);
  }
}

export function requireRole(role: "USER" | "SUPERADMIN") {
  return async (c: Context, next: Next) => {
    const user = c.get("user");

    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    // Check role hierarchy: SUPERADMIN > USER
    const roleHierarchy = { USER: 0, SUPERADMIN: 2 };
    const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] ?? 0;
    const requiredLevel = roleHierarchy[role] ?? 0;

    if (userLevel < requiredLevel) {
      return c.json({ error: "Insufficient permissions" }, 403);
    }

    await next();
  };
}
